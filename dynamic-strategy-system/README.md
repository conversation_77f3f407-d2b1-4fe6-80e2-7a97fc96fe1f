# 动态算法策略系统

## 概述

动态算法策略系统是一个智能的负载均衡和策略路由系统，专门为精排模型的动态打分策略设计。系统能够根据实时系统负载、时间规律和业务需求，自动调整精排模型的打分数量和并行度，在保证系统稳定性的前提下最大化业务指标。

## 核心特性

- **动态策略调整**: 根据系统QPS、RT等指标自动选择最优策略
- **AB分桶路由**: 支持多策略并行运行和流量分配
- **时间感知**: 支持基于时间的策略偏好配置
- **实时监控**: 持续监控系统指标和策略效果
- **降级保护**: 异常情况下自动降级到安全策略
- **配置热更新**: 支持配置的实时更新和生效

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   配置管理器     │    │   指标采集器     │    │   策略计算器     │
│ ConfigManager   │    │MetricsCollector │    │StrategyCalculator│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   定时调度器     │
                    │ StrategyScheduler│
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   策略路由器     │
                    │ StrategyRouter  │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   业务系统      │
                    │RecommendationSvc│
                    └─────────────────┘
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置系统

编辑 `config/strategy-config.json` 文件，配置你的系统参数：

```json
{
  "systemConfig": {
    "bizName": "your-service-name",
    "maxCapacityQPS": 10000,
    "safetyMargin": 0.8
  }
}
```

### 3. 启动系统

```bash
python -m src.main
```

### 4. 集成到业务系统

```python
from src import RecommendationService, ScoringRequest

# 初始化服务
service = RecommendationService('config/strategy-config.json')

# 处理推荐请求
request = ScoringRequest(
    user_id="user123",
    items=["item1", "item2", "item3"],
    context={"scene": "homepage"}
)

result = service.get_recommendations("user123", ["item1", "item2"], {"scene": "homepage"})
```

## 配置说明

### 策略配置

系统支持四种预定义策略：

1. **高精度策略** (`high_precision`): 4批次并行，打分1000个item
2. **平衡策略** (`balanced`): 2批次并行，打分500个item  
3. **保守策略** (`conservative`): 1批次，打分200个item
4. **最小策略** (`minimal`): 1批次，打分100个item

### 负载阈值

- **低负载** (< 30% QPS): 可使用高精度策略
- **中等负载** (30-60% QPS): 使用平衡策略
- **高负载** (60-80% QPS): 使用保守策略  
- **临界负载** (> 80% QPS): 使用最小策略

### 时间规则

支持基于时间的策略偏好：

```json
{
  "timeBasedRules": {
    "enabled": true,
    "rules": [
      {
        "name": "深夜低峰期",
        "timeRange": "02:00-06:00",
        "preferredStrategies": ["high_precision", "balanced"]
      }
    ]
  }
}
```

## API接口

### 获取系统状态

```python
system = DynamicStrategySystem('config/strategy-config.json')
status = system.get_system_status()
```

### 强制执行任务

```python
scheduler.force_run_task('strategy_evaluation')
```

### 更新配置

```python
config_manager.update_config('routingConfig.currentStrategy', 'balanced')
```

## 监控指标

系统会采集以下关键指标：

- **QPS**: 每秒请求数
- **RT**: 平均响应时间
- **错误率**: 请求错误比例
- **CPU使用率**: 系统CPU占用
- **内存使用率**: 系统内存占用
- **活跃连接数**: 当前活跃连接

## 定时任务

系统包含以下定时任务：

1. **策略评估** (30秒): 评估当前负载并调整策略
2. **健康检查** (60秒): 检查系统健康状态
3. **配置同步** (5分钟): 同步配置变更
4. **数据清理** (1小时): 清理历史数据

## 扩展开发

### 自定义策略执行器

```python
def custom_scoring_executor(request: ScoringRequest, config: ScoringConfig) -> Dict[str, Any]:
    # 实现自定义打分逻辑
    return {"strategy": "custom", "scores": [...]}

# 注册执行器
strategy_router.register_strategy_executor('custom', custom_scoring_executor)
```

### 自定义指标采集器

```python
def custom_metric_collector() -> float:
    # 实现自定义指标采集
    return get_custom_metric()

# 注册采集器
metrics_collector.register_collector('custom_metric', custom_metric_collector)
```

## 部署建议

1. **生产环境**: 建议使用容器化部署，配置适当的资源限制
2. **监控集成**: 集成Prometheus、Grafana等监控系统
3. **日志管理**: 配置日志轮转和集中收集
4. **配置管理**: 使用配置中心管理配置文件
5. **高可用**: 部署多实例并配置负载均衡

## 故障排查

### 常见问题

1. **策略不生效**: 检查配置文件格式和权限
2. **指标采集失败**: 确认监控系统连接正常
3. **内存泄漏**: 检查历史数据清理任务
4. **配置热更新失败**: 检查文件监控权限

### 日志分析

系统日志包含详细的执行信息：

```
2024-01-01 10:00:00 - INFO - 策略推荐: balanced, 置信度: 0.85, 原因: 系统负载中等，使用平衡策略
2024-01-01 10:00:30 - INFO - 策略更新成功: balanced
```

## 性能优化

1. **指标采集频率**: 根据业务需求调整采集间隔
2. **历史数据大小**: 控制内存中保存的历史数据量
3. **并发控制**: 合理设置线程池大小
4. **缓存策略**: 缓存频繁访问的配置数据

## 贡献指南

欢迎提交Issue和Pull Request来改进系统。请确保：

1. 代码符合PEP8规范
2. 添加适当的单元测试
3. 更新相关文档
4. 通过所有测试用例

## 许可证

MIT License
