{"version": "1.0.0", "lastUpdated": "2024-01-01T00:00:00Z", "systemConfig": {"bizName": "recommendation-service", "maxCapacityQPS": 10000, "safetyMargin": 0.8, "updateIntervalSeconds": 30, "strategyTransitionTimeSeconds": 60}, "loadThresholds": {"low": {"qpsRatio": 0.3, "description": "系统负载较低，可以使用高精度策略"}, "medium": {"qpsRatio": 0.6, "description": "系统负载中等，使用平衡策略"}, "high": {"qpsRatio": 0.8, "description": "系统负载较高，使用保守策略"}, "critical": {"qpsRatio": 0.95, "description": "系统负载临界，使用最小策略"}}, "strategies": {"high_precision": {"name": "高精度策略", "batchCount": 4, "parallelism": 4, "scoringItemCount": 1000, "expectedQPSMultiplier": 4.0, "maxRT": 200, "applicableLoadLevels": ["low"]}, "balanced": {"name": "平衡策略", "batchCount": 2, "parallelism": 2, "scoringItemCount": 500, "expectedQPSMultiplier": 2.0, "maxRT": 150, "applicableLoadLevels": ["low", "medium"]}, "conservative": {"name": "保守策略", "batchCount": 1, "parallelism": 1, "scoringItemCount": 200, "expectedQPSMultiplier": 1.0, "maxRT": 100, "applicableLoadLevels": ["medium", "high"]}, "minimal": {"name": "最小策略", "batchCount": 1, "parallelism": 1, "scoringItemCount": 100, "expectedQPSMultiplier": 1.0, "maxRT": 80, "applicableLoadLevels": ["high", "critical"]}}, "routingConfig": {"currentStrategy": "balanced", "trafficDistribution": {"high_precision": 0, "balanced": 100, "conservative": 0, "minimal": 0}, "abTestConfig": {"enabled": true, "bucketCount": 100, "hashKey": "userId"}}, "timeBasedRules": {"enabled": true, "rules": [{"name": "深夜低峰期", "timeRange": "02:00-06:00", "preferredStrategies": ["high_precision", "balanced"], "priority": 1}, {"name": "工作日高峰期", "timeRange": "09:00-11:00,14:00-16:00,19:00-21:00", "weekdays": [1, 2, 3, 4, 5], "preferredStrategies": ["conservative", "minimal"], "priority": 2}, {"name": "周末", "weekdays": [6, 7], "preferredStrategies": ["balanced", "high_precision"], "priority": 1}]}, "alertConfig": {"enabled": true, "thresholds": {"qpsUtilization": 0.9, "avgRT": 200, "errorRate": 0.01}, "notificationChannels": ["email", "webhook"]}, "fallbackConfig": {"enabled": true, "fallbackStrategy": "minimal", "triggerConditions": {"consecutiveFailures": 3, "highErrorRate": 0.05, "highRT": 300}}}