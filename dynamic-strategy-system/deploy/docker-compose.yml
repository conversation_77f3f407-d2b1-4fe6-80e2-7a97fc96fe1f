version: '3.8'

services:
  dynamic-strategy-system:
    build:
      context: ..
      dockerfile: deploy/Dockerfile
    container_name: dynamic-strategy-system
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ../config:/app/config
      - ../logs:/app/logs
      - strategy-data:/app/data
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
      - CONFIG_PATH=/app/config/strategy-config.json
    networks:
      - strategy-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  prometheus:
    image: prom/prometheus:latest
    container_name: strategy-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - strategy-network

  grafana:
    image: grafana/grafana:latest
    container_name: strategy-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - strategy-network

  redis:
    image: redis:7-alpine
    container_name: strategy-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    networks:
      - strategy-network

volumes:
  strategy-data:
  prometheus-data:
  grafana-data:
  redis-data:

networks:
  strategy-network:
    driver: bridge
