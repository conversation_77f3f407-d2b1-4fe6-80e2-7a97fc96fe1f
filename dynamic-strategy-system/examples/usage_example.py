"""
动态策略系统使用示例
"""
import time
import logging
from pathlib import Path
import sys

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from src import (
    DynamicStrategySystem, 
    RecommendationService, 
    ScoringRequest,
    ConfigManager
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 配置文件路径
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    
    # 创建推荐服务
    service = RecommendationService(str(config_path))
    
    # 模拟推荐请求
    user_id = "user_12345"
    candidate_items = [f"item_{i}" for i in range(1000)]
    context = {"scene": "homepage", "device": "mobile"}
    
    # 获取推荐结果
    result = service.get_recommendations(user_id, candidate_items, context)
    
    print(f"用户: {user_id}")
    print(f"使用策略: {result['strategy']}")
    print(f"打分数量: {result['scored_items']}")
    print(f"执行时间: {result.get('execution_time_ms', 0)}ms")
    print(f"前5个推荐结果: {result['scores'][:5]}")


def example_system_monitoring():
    """系统监控示例"""
    print("\n=== 系统监控示例 ===")
    
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    
    # 创建系统实例
    system = DynamicStrategySystem(str(config_path))
    
    try:
        # 启动系统（非阻塞方式）
        system.config_manager.start_monitoring()
        system.metrics_collector.start_collection()
        system.scheduler.start_scheduler()
        system.running = True
        
        # 运行一段时间收集数据
        print("系统启动，收集指标数据...")
        time.sleep(10)
        
        # 获取系统状态
        status = system.get_system_status()
        
        print(f"系统运行状态: {status['running']}")
        print(f"当前策略: {status['current_strategy']}")
        print(f"流量分配: {status['traffic_distribution']}")
        print(f"系统健康: {status['system_health']}")
        
        if status['current_metrics']:
            metrics = status['current_metrics']
            print(f"当前QPS: {metrics['current_qps']:.2f}")
            print(f"平均RT: {metrics['avg_rt']:.2f}ms")
            print(f"错误率: {metrics['error_rate']:.3f}")
        
        # 查看任务状态
        print("\n任务执行状态:")
        for task_name, task_info in status['task_status'].items():
            print(f"  {task_name}: {'启用' if task_info['enabled'] else '禁用'}, "
                  f"下次执行: {task_info['seconds_until_next']:.0f}秒后")
        
    finally:
        # 停止系统
        system.stop()


def example_config_management():
    """配置管理示例"""
    print("\n=== 配置管理示例 ===")
    
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    
    # 创建配置管理器
    config_manager = ConfigManager(str(config_path))
    
    # 读取配置
    current_strategy = config_manager.get_current_strategy()
    print(f"当前策略: {current_strategy}")
    
    # 获取策略配置
    strategies = config_manager.get_strategies()
    print(f"可用策略: {list(strategies.keys())}")
    
    # 获取负载阈值
    thresholds = config_manager.get_load_thresholds()
    print("负载阈值:")
    for level, config in thresholds.items():
        print(f"  {level}: QPS比例 {config['qpsRatio']}")
    
    # 模拟配置更新
    print("\n模拟配置更新...")
    new_distribution = {
        "high_precision": 20.0,
        "balanced": 60.0,
        "conservative": 20.0,
        "minimal": 0.0
    }
    
    success = config_manager.update_traffic_distribution(new_distribution)
    if success:
        print("流量分配更新成功")
        updated_distribution = config_manager.get_traffic_distribution()
        print(f"新的流量分配: {updated_distribution}")
    else:
        print("流量分配更新失败")


def example_ab_testing():
    """AB测试示例"""
    print("\n=== AB测试示例 ===")
    
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    service = RecommendationService(str(config_path))
    
    # 模拟多个用户的请求
    user_ids = [f"user_{i}" for i in range(100)]
    strategy_counts = {}
    
    for user_id in user_ids:
        request = ScoringRequest(
            user_id=user_id,
            items=[f"item_{i}" for i in range(100)],
            context={"scene": "homepage"}
        )
        
        # 路由到策略
        strategy = service.strategy_router.route_request(request)
        strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
    
    print("AB测试结果 (100个用户):")
    for strategy, count in strategy_counts.items():
        percentage = (count / len(user_ids)) * 100
        print(f"  {strategy}: {count}个用户 ({percentage:.1f}%)")
    
    # 验证流量分配
    current_distribution = service.strategy_router.config_manager.get_traffic_distribution()
    print(f"\n配置的流量分配: {current_distribution}")


def example_strategy_simulation():
    """策略模拟示例"""
    print("\n=== 策略模拟示例 ===")
    
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    service = RecommendationService(str(config_path))
    
    # 模拟不同的流量分配
    test_distributions = [
        {"balanced": 100.0},  # 全部使用平衡策略
        {"high_precision": 50.0, "balanced": 50.0},  # 50-50分配
        {"high_precision": 20.0, "balanced": 60.0, "conservative": 20.0}  # 三策略分配
    ]
    
    user_ids = [f"user_{i}" for i in range(1000)]
    
    for i, distribution in enumerate(test_distributions, 1):
        print(f"\n测试分配 {i}: {distribution}")
        
        # 模拟流量分配
        result = service.strategy_router.simulate_traffic_distribution(user_ids, distribution)
        
        print("实际分配结果:")
        total_users = sum(result.values())
        for strategy, count in result.items():
            percentage = (count / total_users) * 100 if total_users > 0 else 0
            print(f"  {strategy}: {count}个用户 ({percentage:.1f}%)")


def example_performance_testing():
    """性能测试示例"""
    print("\n=== 性能测试示例 ===")
    
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    service = RecommendationService(str(config_path))
    
    # 测试不同策略的性能
    strategies = ['minimal', 'conservative', 'balanced', 'high_precision']
    test_items = [f"item_{i}" for i in range(1000)]
    
    for strategy in strategies:
        # 临时设置策略
        service.strategy_router.config_manager.update_config(
            'routingConfig.currentStrategy', strategy
        )
        service.strategy_router.config_manager.update_traffic_distribution({strategy: 100.0})
        
        # 刷新缓存
        service.strategy_router._refresh_cache()
        
        # 执行测试
        start_time = time.time()
        
        for i in range(10):  # 执行10次请求
            result = service.get_recommendations(f"test_user_{i}", test_items)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10 * 1000  # 转换为毫秒
        
        print(f"{strategy} 策略:")
        print(f"  平均执行时间: {avg_time:.2f}ms")
        print(f"  打分数量: {result['scored_items']}")
        print(f"  配置: 批次={result['config']['batch_count']}, "
              f"并行度={result['config']['parallelism']}")


if __name__ == '__main__':
    try:
        # 运行所有示例
        example_basic_usage()
        example_config_management()
        example_ab_testing()
        example_strategy_simulation()
        example_performance_testing()
        example_system_monitoring()
        
    except KeyboardInterrupt:
        print("\n示例程序被中断")
    except Exception as e:
        logger.error(f"示例程序执行失败: {e}")
        import traceback
        traceback.print_exc()
