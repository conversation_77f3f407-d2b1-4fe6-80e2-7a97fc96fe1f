"""
配置管理器 - 负责策略配置的读取、更新和监听
"""
import json
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """策略配置管理器"""
    
    def __init__(self, config_path: str, update_interval: int = 30):
        self.config_path = Path(config_path)
        self.update_interval = update_interval
        self.config_data: Dict[str, Any] = {}
        self.last_modified = 0
        self.listeners: list[Callable] = []
        self._lock = threading.RLock()
        self._stop_event = threading.Event()
        self._monitor_thread: Optional[threading.Thread] = None
        
        # 初始化加载配置
        self.load_config()
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                logger.error(f"配置文件不存在: {self.config_path}")
                return False
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                new_config = json.load(f)
                
            with self._lock:
                self.config_data = new_config
                self.last_modified = self.config_path.stat().st_mtime
                
            logger.info("配置加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return False
    
    def save_config(self, config_data: Dict[str, Any]) -> bool:
        """保存配置到文件"""
        try:
            # 更新时间戳
            config_data['lastUpdated'] = datetime.now().isoformat() + 'Z'
            
            # 创建备份
            backup_path = self.config_path.with_suffix('.json.bak')
            if self.config_path.exists():
                self.config_path.rename(backup_path)
            
            # 保存新配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            with self._lock:
                self.config_data = config_data
                self.last_modified = self.config_path.stat().st_mtime
            
            # 通知监听器
            self._notify_listeners()
            
            logger.info("配置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            # 恢复备份
            if backup_path.exists():
                backup_path.rename(self.config_path)
            return False
    
    def get_config(self, key_path: str = None) -> Any:
        """获取配置值"""
        with self._lock:
            if key_path is None:
                return self.config_data.copy()
            
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return None
            
            return value
    
    def update_config(self, key_path: str, value: Any) -> bool:
        """更新配置值"""
        try:
            with self._lock:
                config_copy = self.config_data.copy()
                keys = key_path.split('.')
                current = config_copy
                
                # 导航到目标位置
                for key in keys[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # 设置值
                current[keys[-1]] = value
                
                return self.save_config(config_copy)
                
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
            return False
    
    def add_listener(self, callback: Callable[[Dict[str, Any]], None]):
        """添加配置变更监听器"""
        self.listeners.append(callback)
    
    def remove_listener(self, callback: Callable):
        """移除配置变更监听器"""
        if callback in self.listeners:
            self.listeners.remove(callback)
    
    def _notify_listeners(self):
        """通知所有监听器"""
        for listener in self.listeners:
            try:
                listener(self.config_data.copy())
            except Exception as e:
                logger.error(f"通知监听器失败: {e}")
    
    def start_monitoring(self):
        """开始监控配置文件变化"""
        if self._monitor_thread is not None:
            return
            
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info("配置监控已启动")
    
    def stop_monitoring(self):
        """停止监控配置文件变化"""
        if self._monitor_thread is None:
            return
            
        self._stop_event.set()
        self._monitor_thread.join(timeout=5)
        self._monitor_thread = None
        logger.info("配置监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while not self._stop_event.wait(self.update_interval):
            try:
                if not self.config_path.exists():
                    continue
                    
                current_mtime = self.config_path.stat().st_mtime
                if current_mtime > self.last_modified:
                    logger.info("检测到配置文件变化，重新加载")
                    if self.load_config():
                        self._notify_listeners()
                        
            except Exception as e:
                logger.error(f"监控配置文件失败: {e}")
    
    def get_current_strategy(self) -> str:
        """获取当前策略"""
        return self.get_config('routingConfig.currentStrategy') or 'balanced'
    
    def get_traffic_distribution(self) -> Dict[str, float]:
        """获取流量分配"""
        return self.get_config('routingConfig.trafficDistribution') or {}
    
    def update_traffic_distribution(self, distribution: Dict[str, float]) -> bool:
        """更新流量分配"""
        return self.update_config('routingConfig.trafficDistribution', distribution)
    
    def get_strategies(self) -> Dict[str, Any]:
        """获取所有策略配置"""
        return self.get_config('strategies') or {}
    
    def get_load_thresholds(self) -> Dict[str, Any]:
        """获取负载阈值配置"""
        return self.get_config('loadThresholds') or {}
