"""
动态策略系统主程序
"""
import logging
import signal
import sys
import time
from pathlib import Path

from .config_manager import ConfigManager
from .metrics_collector import MetricsCollector
from .scheduler import StrategyScheduler
from .strategy_router import RecommendationService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dynamic_strategy.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class DynamicStrategySystem:
    """动态策略系统主类"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        
        # 初始化组件
        self.config_manager = ConfigManager(config_path)
        self.metrics_collector = MetricsCollector()
        self.scheduler = StrategyScheduler(self.config_manager, self.metrics_collector)
        self.recommendation_service = RecommendationService(config_path)
        
        # 系统状态
        self.running = False
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"接收到信号 {signum}，准备关闭系统")
        self.stop()
    
    def start(self):
        """启动系统"""
        try:
            logger.info("启动动态策略系统...")
            
            # 启动配置监控
            self.config_manager.start_monitoring()
            
            # 启动指标采集
            self.metrics_collector.start_collection()
            
            # 启动调度器
            self.scheduler.start_scheduler()
            
            self.running = True
            logger.info("动态策略系统启动成功")
            
            # 主循环
            self._main_loop()
            
        except Exception as e:
            logger.error(f"启动系统失败: {e}")
            self.stop()
            sys.exit(1)
    
    def stop(self):
        """停止系统"""
        if not self.running:
            return
        
        logger.info("停止动态策略系统...")
        
        try:
            # 停止调度器
            self.scheduler.stop_scheduler()
            
            # 停止指标采集
            self.metrics_collector.stop_collection()
            
            # 停止配置监控
            self.config_manager.stop_monitoring()
            
            self.running = False
            logger.info("动态策略系统已停止")
            
        except Exception as e:
            logger.error(f"停止系统时出错: {e}")
    
    def _main_loop(self):
        """主循环"""
        while self.running:
            try:
                time.sleep(10)  # 每10秒检查一次系统状态
                
                # 检查系统健康状态
                if not self._check_system_health():
                    logger.warning("系统健康检查失败")
                
            except KeyboardInterrupt:
                logger.info("接收到键盘中断，停止系统")
                break
            except Exception as e:
                logger.error(f"主循环异常: {e}")
    
    def _check_system_health(self) -> bool:
        """检查系统健康状态"""
        try:
            # 检查各组件状态
            current_metrics = self.metrics_collector.get_current_metrics()
            if not current_metrics:
                return False
            
            # 检查配置是否正常
            current_strategy = self.config_manager.get_current_strategy()
            if not current_strategy:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return False
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        try:
            current_metrics = self.metrics_collector.get_current_metrics()
            task_status = self.scheduler.get_task_status()
            strategy_stats = self.recommendation_service.strategy_router.get_strategy_statistics()
            
            return {
                'running': self.running,
                'current_metrics': current_metrics.__dict__ if current_metrics else None,
                'current_strategy': self.config_manager.get_current_strategy(),
                'traffic_distribution': self.config_manager.get_traffic_distribution(),
                'task_status': task_status,
                'strategy_statistics': strategy_stats,
                'system_health': self.metrics_collector.is_system_healthy()
            }
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {'error': str(e)}


def main():
    """主函数"""
    # 配置文件路径
    config_path = Path(__file__).parent.parent / 'config' / 'strategy-config.json'
    
    if not config_path.exists():
        logger.error(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 创建并启动系统
    system = DynamicStrategySystem(str(config_path))
    
    try:
        system.start()
    except KeyboardInterrupt:
        logger.info("接收到中断信号")
    finally:
        system.stop()


if __name__ == '__main__':
    main()
