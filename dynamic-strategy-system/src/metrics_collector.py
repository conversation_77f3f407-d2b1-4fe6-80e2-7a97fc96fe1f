"""
指标采集器 - 负责收集系统实时指标
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from collections import deque
import logging

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标数据结构"""
    timestamp: datetime
    current_qps: float
    avg_rt: float
    error_rate: float
    cpu_usage: float
    memory_usage: float
    active_connections: int


class MetricsCollector:
    """系统指标采集器"""
    
    def __init__(self, collection_interval: int = 10, history_size: int = 360):
        self.collection_interval = collection_interval
        self.history_size = history_size
        self.metrics_history: deque[SystemMetrics] = deque(maxlen=history_size)
        self.current_metrics: Optional[SystemMetrics] = None
        
        self._collectors: Dict[str, Callable] = {}
        self._lock = threading.RLock()
        self._stop_event = threading.Event()
        self._collection_thread: Optional[threading.Thread] = None
        
        # 注册默认采集器
        self._register_default_collectors()
    
    def _register_default_collectors(self):
        """注册默认的指标采集器"""
        self.register_collector('qps', self._collect_qps)
        self.register_collector('rt', self._collect_response_time)
        self.register_collector('error_rate', self._collect_error_rate)
        self.register_collector('cpu', self._collect_cpu_usage)
        self.register_collector('memory', self._collect_memory_usage)
        self.register_collector('connections', self._collect_connections)
    
    def register_collector(self, name: str, collector_func: Callable[[], float]):
        """注册指标采集器"""
        self._collectors[name] = collector_func
    
    def _collect_qps(self) -> float:
        """采集QPS指标 - 需要根据实际系统实现"""
        # 这里应该连接到实际的监控系统，如Prometheus、CloudWatch等
        # 示例实现：从日志或监控API获取QPS
        try:
            # 模拟从监控系统获取QPS
            # 实际实现中应该调用监控API
            return self._get_metric_from_monitoring_system('qps')
        except Exception as e:
            logger.error(f"采集QPS失败: {e}")
            return 0.0
    
    def _collect_response_time(self) -> float:
        """采集平均响应时间"""
        try:
            return self._get_metric_from_monitoring_system('avg_response_time')
        except Exception as e:
            logger.error(f"采集响应时间失败: {e}")
            return 0.0
    
    def _collect_error_rate(self) -> float:
        """采集错误率"""
        try:
            return self._get_metric_from_monitoring_system('error_rate')
        except Exception as e:
            logger.error(f"采集错误率失败: {e}")
            return 0.0
    
    def _collect_cpu_usage(self) -> float:
        """采集CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=1)
        except ImportError:
            logger.warning("psutil未安装，无法采集CPU指标")
            return 0.0
        except Exception as e:
            logger.error(f"采集CPU使用率失败: {e}")
            return 0.0
    
    def _collect_memory_usage(self) -> float:
        """采集内存使用率"""
        try:
            import psutil
            return psutil.virtual_memory().percent
        except ImportError:
            logger.warning("psutil未安装，无法采集内存指标")
            return 0.0
        except Exception as e:
            logger.error(f"采集内存使用率失败: {e}")
            return 0.0
    
    def _collect_connections(self) -> int:
        """采集活跃连接数"""
        try:
            return self._get_metric_from_monitoring_system('active_connections')
        except Exception as e:
            logger.error(f"采集连接数失败: {e}")
            return 0
    
    def _get_metric_from_monitoring_system(self, metric_name: str) -> float:
        """从监控系统获取指标 - 需要根据实际监控系统实现"""
        # 这里应该实现具体的监控系统集成
        # 例如：Prometheus、Grafana、CloudWatch、自定义监控等
        
        # 示例：Prometheus集成
        # try:
        #     from prometheus_client.parser import text_string_to_metric_families
        #     import requests
        #     
        #     response = requests.get('http://prometheus:9090/api/v1/query', 
        #                           params={'query': f'{metric_name}'})
        #     data = response.json()
        #     return float(data['data']['result'][0]['value'][1])
        # except Exception as e:
        #     logger.error(f"从Prometheus获取{metric_name}失败: {e}")
        #     return 0.0
        
        # 模拟数据（实际使用时需要替换）
        import random
        mock_data = {
            'qps': random.uniform(1000, 8000),
            'avg_response_time': random.uniform(50, 200),
            'error_rate': random.uniform(0, 0.02),
            'active_connections': random.randint(100, 1000)
        }
        return mock_data.get(metric_name, 0.0)
    
    def collect_current_metrics(self) -> SystemMetrics:
        """采集当前系统指标"""
        try:
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                current_qps=self._collectors['qps'](),
                avg_rt=self._collectors['rt'](),
                error_rate=self._collectors['error_rate'](),
                cpu_usage=self._collectors['cpu'](),
                memory_usage=self._collectors['memory'](),
                active_connections=int(self._collectors['connections']())
            )
            
            with self._lock:
                self.current_metrics = metrics
                self.metrics_history.append(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"采集系统指标失败: {e}")
            return SystemMetrics(
                timestamp=datetime.now(),
                current_qps=0, avg_rt=0, error_rate=0,
                cpu_usage=0, memory_usage=0, active_connections=0
            )
    
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """获取当前指标"""
        with self._lock:
            return self.current_metrics
    
    def get_metrics_history(self, duration_minutes: int = 30) -> List[SystemMetrics]:
        """获取历史指标"""
        cutoff_time = datetime.now() - timedelta(minutes=duration_minutes)
        
        with self._lock:
            return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_average_metrics(self, duration_minutes: int = 5) -> Optional[SystemMetrics]:
        """获取指定时间段的平均指标"""
        history = self.get_metrics_history(duration_minutes)
        
        if not history:
            return None
        
        total_qps = sum(m.current_qps for m in history)
        total_rt = sum(m.avg_rt for m in history)
        total_error_rate = sum(m.error_rate for m in history)
        total_cpu = sum(m.cpu_usage for m in history)
        total_memory = sum(m.memory_usage for m in history)
        total_connections = sum(m.active_connections for m in history)
        
        count = len(history)
        
        return SystemMetrics(
            timestamp=datetime.now(),
            current_qps=total_qps / count,
            avg_rt=total_rt / count,
            error_rate=total_error_rate / count,
            cpu_usage=total_cpu / count,
            memory_usage=total_memory / count,
            active_connections=int(total_connections / count)
        )
    
    def start_collection(self):
        """开始指标采集"""
        if self._collection_thread is not None:
            return
        
        self._stop_event.clear()
        self._collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self._collection_thread.start()
        logger.info("指标采集已启动")
    
    def stop_collection(self):
        """停止指标采集"""
        if self._collection_thread is None:
            return
        
        self._stop_event.set()
        self._collection_thread.join(timeout=5)
        self._collection_thread = None
        logger.info("指标采集已停止")
    
    def _collection_loop(self):
        """采集循环"""
        while not self._stop_event.wait(self.collection_interval):
            try:
                self.collect_current_metrics()
            except Exception as e:
                logger.error(f"指标采集循环异常: {e}")
    
    def is_system_healthy(self) -> bool:
        """检查系统是否健康"""
        current = self.get_current_metrics()
        if not current:
            return False
        
        # 定义健康阈值
        return (
            current.error_rate < 0.05 and  # 错误率小于5%
            current.avg_rt < 300 and       # 平均响应时间小于300ms
            current.cpu_usage < 90 and     # CPU使用率小于90%
            current.memory_usage < 90      # 内存使用率小于90%
        )
