"""
定时调度器 - 负责定时执行策略调整任务
"""
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Callable, Dict, Any
from dataclasses import dataclass

from .config_manager import ConfigManager
from .metrics_collector import MetricsCollector
from .strategy_calculator import StrategyCalculator, StrategyRecommendation

logger = logging.getLogger(__name__)


@dataclass
class ScheduleTask:
    """调度任务"""
    name: str
    interval_seconds: int
    last_run: Optional[datetime]
    next_run: datetime
    callback: Callable
    enabled: bool = True


class StrategyScheduler:
    """策略调度器"""
    
    def __init__(self, config_manager: ConfigManager, metrics_collector: MetricsCollector):
        self.config_manager = config_manager
        self.metrics_collector = metrics_collector
        self.strategy_calculator = StrategyCalculator(config_manager, metrics_collector)
        
        self.tasks: Dict[str, ScheduleTask] = {}
        self._scheduler_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        self._lock = threading.RLock()
        
        # 注册默认任务
        self._register_default_tasks()
    
    def _register_default_tasks(self):
        """注册默认调度任务"""
        # 策略评估任务
        self.register_task(
            name="strategy_evaluation",
            interval_seconds=30,
            callback=self._evaluate_and_update_strategy
        )
        
        # 系统健康检查任务
        self.register_task(
            name="health_check",
            interval_seconds=60,
            callback=self._perform_health_check
        )
        
        # 配置同步任务
        self.register_task(
            name="config_sync",
            interval_seconds=300,  # 5分钟
            callback=self._sync_configuration
        )
        
        # 清理历史数据任务
        self.register_task(
            name="cleanup_history",
            interval_seconds=3600,  # 1小时
            callback=self._cleanup_history_data
        )
    
    def register_task(self, name: str, interval_seconds: int, callback: Callable, enabled: bool = True):
        """注册调度任务"""
        with self._lock:
            self.tasks[name] = ScheduleTask(
                name=name,
                interval_seconds=interval_seconds,
                last_run=None,
                next_run=datetime.now(),
                callback=callback,
                enabled=enabled
            )
        logger.info(f"注册调度任务: {name}, 间隔: {interval_seconds}秒")
    
    def unregister_task(self, name: str):
        """注销调度任务"""
        with self._lock:
            if name in self.tasks:
                del self.tasks[name]
                logger.info(f"注销调度任务: {name}")
    
    def enable_task(self, name: str):
        """启用任务"""
        with self._lock:
            if name in self.tasks:
                self.tasks[name].enabled = True
                logger.info(f"启用任务: {name}")
    
    def disable_task(self, name: str):
        """禁用任务"""
        with self._lock:
            if name in self.tasks:
                self.tasks[name].enabled = False
                logger.info(f"禁用任务: {name}")
    
    def start_scheduler(self):
        """启动调度器"""
        if self._scheduler_thread is not None:
            logger.warning("调度器已经在运行")
            return
        
        self._stop_event.clear()
        self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._scheduler_thread.start()
        logger.info("策略调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        if self._scheduler_thread is None:
            return
        
        self._stop_event.set()
        self._scheduler_thread.join(timeout=10)
        self._scheduler_thread = None
        logger.info("策略调度器已停止")
    
    def _scheduler_loop(self):
        """调度循环"""
        while not self._stop_event.wait(5):  # 每5秒检查一次
            try:
                current_time = datetime.now()
                
                with self._lock:
                    for task in self.tasks.values():
                        if not task.enabled:
                            continue
                        
                        if current_time >= task.next_run:
                            self._execute_task(task, current_time)
                            
            except Exception as e:
                logger.error(f"调度循环异常: {e}")
    
    def _execute_task(self, task: ScheduleTask, current_time: datetime):
        """执行任务"""
        try:
            logger.debug(f"执行任务: {task.name}")
            task.callback()
            
            # 更新任务状态
            task.last_run = current_time
            task.next_run = current_time + timedelta(seconds=task.interval_seconds)
            
        except Exception as e:
            logger.error(f"执行任务 {task.name} 失败: {e}")
            # 任务失败时延迟重试
            task.next_run = current_time + timedelta(seconds=min(task.interval_seconds, 60))
    
    def _evaluate_and_update_strategy(self):
        """评估并更新策略"""
        try:
            # 计算最优策略
            recommendation = self.strategy_calculator.calculate_optimal_strategy()
            if not recommendation:
                logger.warning("无法获取策略推荐")
                return
            
            current_strategy = self.config_manager.get_current_strategy()
            current_distribution = self.config_manager.get_traffic_distribution()
            
            # 检查是否需要更新
            if (recommendation.strategy_name != current_strategy or 
                recommendation.traffic_distribution != current_distribution):
                
                logger.info(f"策略推荐: {recommendation.strategy_name}, "
                          f"置信度: {recommendation.confidence:.2f}, "
                          f"原因: {recommendation.reason}")
                
                # 验证策略可行性
                current_metrics = self.metrics_collector.get_current_metrics()
                if current_metrics:
                    feasible, reason = self.strategy_calculator.validate_strategy_feasibility(
                        recommendation.strategy_name, current_metrics
                    )
                    
                    if not feasible:
                        logger.warning(f"策略不可行: {reason}")
                        return
                
                # 更新配置
                success = self._apply_strategy_recommendation(recommendation)
                if success:
                    logger.info(f"策略更新成功: {recommendation.strategy_name}")
                else:
                    logger.error("策略更新失败")
            
        except Exception as e:
            logger.error(f"策略评估失败: {e}")
    
    def _apply_strategy_recommendation(self, recommendation: StrategyRecommendation) -> bool:
        """应用策略推荐"""
        try:
            # 更新当前策略
            if not self.config_manager.update_config(
                'routingConfig.currentStrategy', 
                recommendation.strategy_name
            ):
                return False
            
            # 更新流量分配
            if not self.config_manager.update_traffic_distribution(
                recommendation.traffic_distribution
            ):
                return False
            
            # 记录策略变更日志
            self._log_strategy_change(recommendation)
            
            return True
            
        except Exception as e:
            logger.error(f"应用策略推荐失败: {e}")
            return False
    
    def _log_strategy_change(self, recommendation: StrategyRecommendation):
        """记录策略变更日志"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'strategy': recommendation.strategy_name,
            'confidence': recommendation.confidence,
            'reason': recommendation.reason,
            'traffic_distribution': recommendation.traffic_distribution,
            'expected_qps_impact': recommendation.expected_qps_impact
        }
        
        # 这里可以将日志写入文件或发送到日志系统
        logger.info(f"策略变更记录: {log_entry}")
    
    def _perform_health_check(self):
        """执行系统健康检查"""
        try:
            current_metrics = self.metrics_collector.get_current_metrics()
            if not current_metrics:
                logger.warning("无法获取系统指标进行健康检查")
                return
            
            is_healthy = self.metrics_collector.is_system_healthy()
            
            if not is_healthy:
                logger.warning(f"系统健康检查失败: QPS={current_metrics.current_qps}, "
                             f"RT={current_metrics.avg_rt}ms, "
                             f"错误率={current_metrics.error_rate:.3f}")
                
                # 触发降级策略
                self._trigger_fallback_strategy()
            else:
                logger.debug("系统健康检查通过")
                
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    def _trigger_fallback_strategy(self):
        """触发降级策略"""
        try:
            fallback_config = self.config_manager.get_config('fallbackConfig')
            if not fallback_config or not fallback_config.get('enabled'):
                return
            
            fallback_strategy = fallback_config.get('fallbackStrategy', 'minimal')
            
            logger.warning(f"触发降级策略: {fallback_strategy}")
            
            # 立即切换到降级策略
            self.config_manager.update_config('routingConfig.currentStrategy', fallback_strategy)
            self.config_manager.update_traffic_distribution({fallback_strategy: 100.0})
            
        except Exception as e:
            logger.error(f"触发降级策略失败: {e}")
    
    def _sync_configuration(self):
        """同步配置"""
        try:
            # 这里可以实现配置的远程同步逻辑
            # 例如从配置中心拉取最新配置
            logger.debug("配置同步检查完成")
            
        except Exception as e:
            logger.error(f"配置同步失败: {e}")
    
    def _cleanup_history_data(self):
        """清理历史数据"""
        try:
            # 清理过期的指标数据
            # metrics_collector 已经通过 deque 自动限制了历史数据大小
            
            logger.debug("历史数据清理完成")
            
        except Exception as e:
            logger.error(f"清理历史数据失败: {e}")
    
    def get_task_status(self) -> Dict[str, Dict[str, Any]]:
        """获取任务状态"""
        with self._lock:
            status = {}
            for name, task in self.tasks.items():
                status[name] = {
                    'enabled': task.enabled,
                    'interval_seconds': task.interval_seconds,
                    'last_run': task.last_run.isoformat() if task.last_run else None,
                    'next_run': task.next_run.isoformat(),
                    'seconds_until_next': (task.next_run - datetime.now()).total_seconds()
                }
            return status
    
    def force_run_task(self, task_name: str) -> bool:
        """强制执行任务"""
        with self._lock:
            if task_name not in self.tasks:
                logger.error(f"任务不存在: {task_name}")
                return False
            
            task = self.tasks[task_name]
            if not task.enabled:
                logger.warning(f"任务已禁用: {task_name}")
                return False
            
            try:
                self._execute_task(task, datetime.now())
                logger.info(f"强制执行任务成功: {task_name}")
                return True
            except Exception as e:
                logger.error(f"强制执行任务失败: {task_name}, {e}")
                return False
