"""
策略计算器 - 根据系统负载计算最优策略
"""
import logging
from datetime import datetime, time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from .metrics_collector import SystemMetrics, MetricsCollector
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)


@dataclass
class StrategyRecommendation:
    """策略推荐结果"""
    strategy_name: str
    confidence: float
    reason: str
    traffic_distribution: Dict[str, float]
    expected_qps_impact: float


class StrategyCalculator:
    """策略计算器"""
    
    def __init__(self, config_manager: ConfigManager, metrics_collector: MetricsCollector):
        self.config_manager = config_manager
        self.metrics_collector = metrics_collector
    
    def calculate_optimal_strategy(self) -> Optional[StrategyRecommendation]:
        """计算最优策略"""
        try:
            # 获取当前系统指标
            current_metrics = self.metrics_collector.get_current_metrics()
            if not current_metrics:
                logger.warning("无法获取当前系统指标")
                return None
            
            # 获取平均指标（过去5分钟）
            avg_metrics = self.metrics_collector.get_average_metrics(5)
            if not avg_metrics:
                avg_metrics = current_metrics
            
            # 计算系统负载水平
            load_level = self._calculate_load_level(avg_metrics)
            
            # 获取时间相关的策略偏好
            time_preferences = self._get_time_based_preferences()
            
            # 选择最优策略
            optimal_strategy = self._select_optimal_strategy(
                load_level, time_preferences, avg_metrics
            )
            
            # 计算流量分配
            traffic_distribution = self._calculate_traffic_distribution(
                optimal_strategy, load_level
            )
            
            # 评估策略影响
            qps_impact = self._estimate_qps_impact(optimal_strategy, avg_metrics)
            
            return StrategyRecommendation(
                strategy_name=optimal_strategy,
                confidence=self._calculate_confidence(load_level, avg_metrics),
                reason=self._generate_reason(load_level, optimal_strategy),
                traffic_distribution=traffic_distribution,
                expected_qps_impact=qps_impact
            )
            
        except Exception as e:
            logger.error(f"计算最优策略失败: {e}")
            return None
    
    def _calculate_load_level(self, metrics: SystemMetrics) -> str:
        """计算系统负载水平"""
        system_config = self.config_manager.get_config('systemConfig')
        load_thresholds = self.config_manager.get_load_thresholds()
        
        max_qps = system_config.get('maxCapacityQPS', 10000)
        safety_margin = system_config.get('safetyMargin', 0.8)
        
        # 计算QPS利用率
        qps_ratio = metrics.current_qps / (max_qps * safety_margin)
        
        # 根据阈值确定负载水平
        if qps_ratio >= load_thresholds['critical']['qpsRatio']:
            return 'critical'
        elif qps_ratio >= load_thresholds['high']['qpsRatio']:
            return 'high'
        elif qps_ratio >= load_thresholds['medium']['qpsRatio']:
            return 'medium'
        else:
            return 'low'
    
    def _get_time_based_preferences(self) -> List[str]:
        """获取基于时间的策略偏好"""
        time_rules = self.config_manager.get_config('timeBasedRules')
        if not time_rules or not time_rules.get('enabled'):
            return []
        
        current_time = datetime.now()
        current_weekday = current_time.weekday() + 1  # 1=Monday, 7=Sunday
        current_time_str = current_time.strftime('%H:%M')
        
        applicable_rules = []
        
        for rule in time_rules.get('rules', []):
            # 检查星期
            if 'weekdays' in rule and current_weekday not in rule['weekdays']:
                continue
            
            # 检查时间范围
            if 'timeRange' in rule:
                if self._is_time_in_range(current_time_str, rule['timeRange']):
                    applicable_rules.append(rule)
            else:
                applicable_rules.append(rule)
        
        # 按优先级排序
        applicable_rules.sort(key=lambda x: x.get('priority', 0), reverse=True)
        
        # 返回首个匹配规则的偏好策略
        if applicable_rules:
            return applicable_rules[0].get('preferredStrategies', [])
        
        return []
    
    def _is_time_in_range(self, current_time: str, time_range: str) -> bool:
        """检查当前时间是否在指定范围内"""
        try:
            ranges = time_range.split(',')
            current = datetime.strptime(current_time, '%H:%M').time()
            
            for range_str in ranges:
                start_str, end_str = range_str.strip().split('-')
                start_time = datetime.strptime(start_str, '%H:%M').time()
                end_time = datetime.strptime(end_str, '%H:%M').time()
                
                if start_time <= current <= end_time:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"解析时间范围失败: {e}")
            return False
    
    def _select_optimal_strategy(self, load_level: str, time_preferences: List[str], 
                               metrics: SystemMetrics) -> str:
        """选择最优策略"""
        strategies = self.config_manager.get_strategies()
        
        # 过滤适用于当前负载水平的策略
        applicable_strategies = []
        for name, config in strategies.items():
            if load_level in config.get('applicableLoadLevels', []):
                applicable_strategies.append(name)
        
        if not applicable_strategies:
            logger.warning(f"没有适用于负载水平 {load_level} 的策略")
            return 'conservative'  # 默认策略
        
        # 优先考虑时间偏好
        for preferred in time_preferences:
            if preferred in applicable_strategies:
                return preferred
        
        # 根据负载水平选择默认策略
        load_strategy_map = {
            'low': 'high_precision',
            'medium': 'balanced',
            'high': 'conservative',
            'critical': 'minimal'
        }
        
        preferred_strategy = load_strategy_map.get(load_level, 'balanced')
        if preferred_strategy in applicable_strategies:
            return preferred_strategy
        
        # 返回第一个适用策略
        return applicable_strategies[0]
    
    def _calculate_traffic_distribution(self, strategy_name: str, load_level: str) -> Dict[str, float]:
        """计算流量分配"""
        # 根据负载水平决定是否需要渐进式切换
        if load_level in ['critical', 'high']:
            # 高负载时立即切换
            return {strategy_name: 100.0}
        
        # 低负载时可以考虑AB测试
        current_distribution = self.config_manager.get_traffic_distribution()
        current_strategy = self.config_manager.get_current_strategy()
        
        if current_strategy == strategy_name:
            return current_distribution
        
        # 渐进式切换：每次调整20%
        new_distribution = current_distribution.copy()
        
        # 减少当前策略的流量
        if current_strategy in new_distribution:
            current_traffic = new_distribution[current_strategy]
            reduction = min(20.0, current_traffic)
            new_distribution[current_strategy] = current_traffic - reduction
            
            # 增加新策略的流量
            new_distribution[strategy_name] = new_distribution.get(strategy_name, 0) + reduction
        else:
            new_distribution[strategy_name] = 100.0
        
        return new_distribution
    
    def _estimate_qps_impact(self, strategy_name: str, metrics: SystemMetrics) -> float:
        """估算策略对QPS的影响"""
        strategies = self.config_manager.get_strategies()
        strategy_config = strategies.get(strategy_name, {})
        
        qps_multiplier = strategy_config.get('expectedQPSMultiplier', 1.0)
        return metrics.current_qps * (qps_multiplier - 1.0)
    
    def _calculate_confidence(self, load_level: str, metrics: SystemMetrics) -> float:
        """计算推荐置信度"""
        confidence = 0.8  # 基础置信度
        
        # 根据系统健康状况调整
        if self.metrics_collector.is_system_healthy():
            confidence += 0.1
        else:
            confidence -= 0.2
        
        # 根据负载水平调整
        load_confidence_map = {
            'low': 0.9,
            'medium': 0.8,
            'high': 0.7,
            'critical': 0.6
        }
        
        confidence = min(confidence, load_confidence_map.get(load_level, 0.5))
        
        return max(0.1, min(1.0, confidence))
    
    def _generate_reason(self, load_level: str, strategy_name: str) -> str:
        """生成推荐原因"""
        load_descriptions = {
            'low': '系统负载较低',
            'medium': '系统负载中等',
            'high': '系统负载较高',
            'critical': '系统负载临界'
        }
        
        strategy_descriptions = {
            'high_precision': '使用高精度策略以提升业务指标',
            'balanced': '使用平衡策略保持性能和效果的平衡',
            'conservative': '使用保守策略确保系统稳定',
            'minimal': '使用最小策略保证系统可用性'
        }
        
        load_desc = load_descriptions.get(load_level, '系统负载未知')
        strategy_desc = strategy_descriptions.get(strategy_name, '使用默认策略')
        
        return f"{load_desc}，{strategy_desc}"
    
    def validate_strategy_feasibility(self, strategy_name: str, metrics: SystemMetrics) -> Tuple[bool, str]:
        """验证策略可行性"""
        strategies = self.config_manager.get_strategies()
        strategy_config = strategies.get(strategy_name)
        
        if not strategy_config:
            return False, f"策略 {strategy_name} 不存在"
        
        # 检查QPS容量
        system_config = self.config_manager.get_config('systemConfig')
        max_qps = system_config.get('maxCapacityQPS', 10000)
        safety_margin = system_config.get('safetyMargin', 0.8)
        
        qps_multiplier = strategy_config.get('expectedQPSMultiplier', 1.0)
        expected_qps = metrics.current_qps * qps_multiplier
        
        if expected_qps > max_qps * safety_margin:
            return False, f"策略 {strategy_name} 预期QPS {expected_qps} 超过系统容量"
        
        # 检查RT要求
        max_rt = strategy_config.get('maxRT', 200)
        if metrics.avg_rt > max_rt * 1.2:  # 允许20%的缓冲
            return False, f"当前RT {metrics.avg_rt}ms 超过策略要求 {max_rt}ms"
        
        return True, "策略可行"
