"""
策略路由器 - 负责根据配置进行AB分桶和策略路由
"""
import hashlib
import logging
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from .config_manager import ConfigManager

logger = logging.getLogger(__name__)


class StrategyType(Enum):
    """策略类型枚举"""
    HIGH_PRECISION = "high_precision"
    BALANCED = "balanced"
    CONSERVATIVE = "conservative"
    MINIMAL = "minimal"


@dataclass
class ScoringRequest:
    """打分请求"""
    user_id: str
    items: list
    context: Dict[str, Any]


@dataclass
class ScoringConfig:
    """打分配置"""
    batch_count: int
    parallelism: int
    scoring_item_count: int
    max_rt: int


class StrategyRouter:
    """策略路由器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.strategy_executors: Dict[str, Callable] = {}
        
        # 注册配置变更监听器
        self.config_manager.add_listener(self._on_config_changed)
        
        # 缓存当前配置
        self._current_distribution = {}
        self._current_strategies = {}
        self._refresh_cache()
    
    def _refresh_cache(self):
        """刷新缓存"""
        self._current_distribution = self.config_manager.get_traffic_distribution()
        self._current_strategies = self.config_manager.get_strategies()
    
    def _on_config_changed(self, config: Dict[str, Any]):
        """配置变更回调"""
        logger.info("检测到配置变更，刷新路由缓存")
        self._refresh_cache()
    
    def register_strategy_executor(self, strategy_name: str, executor: Callable):
        """注册策略执行器"""
        self.strategy_executors[strategy_name] = executor
        logger.info(f"注册策略执行器: {strategy_name}")
    
    def route_request(self, request: ScoringRequest) -> str:
        """路由请求到对应策略"""
        try:
            # 获取AB测试配置
            ab_config = self.config_manager.get_config('routingConfig.abTestConfig')
            if not ab_config or not ab_config.get('enabled'):
                # AB测试未启用，使用当前策略
                return self.config_manager.get_current_strategy()
            
            # 计算用户分桶
            bucket = self._calculate_user_bucket(
                request.user_id, 
                ab_config.get('bucketCount', 100),
                ab_config.get('hashKey', 'userId')
            )
            
            # 根据流量分配选择策略
            strategy = self._select_strategy_by_bucket(bucket)
            
            logger.debug(f"用户 {request.user_id} 路由到策略: {strategy} (bucket: {bucket})")
            return strategy
            
        except Exception as e:
            logger.error(f"请求路由失败: {e}")
            # 降级到默认策略
            return 'conservative'
    
    def _calculate_user_bucket(self, user_id: str, bucket_count: int, hash_key: str) -> int:
        """计算用户分桶"""
        # 使用MD5哈希确保分布均匀
        hash_input = f"{hash_key}:{user_id}"
        hash_value = hashlib.md5(hash_input.encode()).hexdigest()
        
        # 取前8位十六进制转为整数
        hash_int = int(hash_value[:8], 16)
        
        # 取模得到分桶
        return hash_int % bucket_count
    
    def _select_strategy_by_bucket(self, bucket: int) -> str:
        """根据分桶选择策略"""
        distribution = self._current_distribution
        
        if not distribution:
            return self.config_manager.get_current_strategy()
        
        # 计算累积分布
        cumulative = 0
        for strategy, percentage in distribution.items():
            cumulative += percentage
            if bucket < cumulative:
                return strategy
        
        # 如果没有匹配到（可能由于配置错误），返回第一个策略
        return list(distribution.keys())[0] if distribution else 'conservative'
    
    def get_scoring_config(self, strategy_name: str) -> Optional[ScoringConfig]:
        """获取策略的打分配置"""
        strategy_config = self._current_strategies.get(strategy_name)
        if not strategy_config:
            logger.error(f"策略配置不存在: {strategy_name}")
            return None
        
        return ScoringConfig(
            batch_count=strategy_config.get('batchCount', 1),
            parallelism=strategy_config.get('parallelism', 1),
            scoring_item_count=strategy_config.get('scoringItemCount', 100),
            max_rt=strategy_config.get('maxRT', 100)
        )
    
    def execute_scoring(self, request: ScoringRequest) -> Dict[str, Any]:
        """执行打分"""
        try:
            # 路由到对应策略
            strategy_name = self.route_request(request)
            
            # 获取打分配置
            scoring_config = self.get_scoring_config(strategy_name)
            if not scoring_config:
                raise ValueError(f"无法获取策略配置: {strategy_name}")
            
            # 执行策略
            if strategy_name in self.strategy_executors:
                return self.strategy_executors[strategy_name](request, scoring_config)
            else:
                # 使用默认执行器
                return self._default_scoring_executor(request, scoring_config, strategy_name)
                
        except Exception as e:
            logger.error(f"执行打分失败: {e}")
            # 降级处理
            return self._fallback_scoring(request)
    
    def _default_scoring_executor(self, request: ScoringRequest, 
                                config: ScoringConfig, strategy_name: str) -> Dict[str, Any]:
        """默认打分执行器"""
        logger.info(f"执行策略: {strategy_name}, 批次数: {config.batch_count}, "
                   f"并行度: {config.parallelism}, 打分数量: {config.scoring_item_count}")
        
        # 这里应该实现具体的打分逻辑
        # 根据配置进行分批并行打分
        
        # 模拟打分结果
        results = {
            'strategy': strategy_name,
            'user_id': request.user_id,
            'scored_items': min(len(request.items), config.scoring_item_count),
            'batch_count': config.batch_count,
            'parallelism': config.parallelism,
            'scores': [
                {'item_id': f'item_{i}', 'score': 0.8 - i * 0.01}
                for i in range(min(len(request.items), config.scoring_item_count))
            ]
        }
        
        return results
    
    def _fallback_scoring(self, request: ScoringRequest) -> Dict[str, Any]:
        """降级打分"""
        logger.warning("执行降级打分")
        
        # 使用最简单的策略
        return {
            'strategy': 'fallback',
            'user_id': request.user_id,
            'scored_items': min(len(request.items), 50),  # 最少打分数量
            'scores': [
                {'item_id': f'item_{i}', 'score': 0.5}
                for i in range(min(len(request.items), 50))
            ]
        }
    
    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        distribution = self._current_distribution
        strategies = self._current_strategies
        
        stats = {
            'current_distribution': distribution,
            'total_strategies': len(strategies),
            'registered_executors': len(self.strategy_executors),
            'strategy_details': {}
        }
        
        for name, config in strategies.items():
            stats['strategy_details'][name] = {
                'batch_count': config.get('batchCount', 1),
                'parallelism': config.get('parallelism', 1),
                'scoring_item_count': config.get('scoringItemCount', 100),
                'expected_qps_multiplier': config.get('expectedQPSMultiplier', 1.0),
                'traffic_percentage': distribution.get(name, 0)
            }
        
        return stats
    
    def validate_traffic_distribution(self, distribution: Dict[str, float]) -> tuple[bool, str]:
        """验证流量分配"""
        try:
            # 检查总和是否为100
            total = sum(distribution.values())
            if abs(total - 100.0) > 0.01:
                return False, f"流量分配总和不等于100%: {total}"
            
            # 检查策略是否存在
            strategies = self._current_strategies
            for strategy_name in distribution.keys():
                if strategy_name not in strategies:
                    return False, f"策略不存在: {strategy_name}"
            
            # 检查百分比是否有效
            for strategy_name, percentage in distribution.items():
                if percentage < 0 or percentage > 100:
                    return False, f"策略 {strategy_name} 的流量百分比无效: {percentage}"
            
            return True, "流量分配验证通过"
            
        except Exception as e:
            return False, f"验证流量分配时出错: {e}"
    
    def simulate_traffic_distribution(self, user_ids: list, distribution: Dict[str, float]) -> Dict[str, int]:
        """模拟流量分配"""
        result = {strategy: 0 for strategy in distribution.keys()}
        
        ab_config = self.config_manager.get_config('routingConfig.abTestConfig')
        bucket_count = ab_config.get('bucketCount', 100) if ab_config else 100
        hash_key = ab_config.get('hashKey', 'userId') if ab_config else 'userId'
        
        for user_id in user_ids:
            bucket = self._calculate_user_bucket(user_id, bucket_count, hash_key)
            
            # 根据分布选择策略
            cumulative = 0
            for strategy, percentage in distribution.items():
                cumulative += percentage
                if bucket < cumulative:
                    result[strategy] += 1
                    break
        
        return result


# 业务系统集成示例
class RecommendationService:
    """推荐服务示例"""
    
    def __init__(self, config_path: str):
        self.config_manager = ConfigManager(config_path)
        self.strategy_router = StrategyRouter(self.config_manager)
        
        # 注册策略执行器
        self._register_strategy_executors()
        
        # 启动配置监控
        self.config_manager.start_monitoring()
    
    def _register_strategy_executors(self):
        """注册策略执行器"""
        self.strategy_router.register_strategy_executor(
            'high_precision', self._high_precision_scoring
        )
        self.strategy_router.register_strategy_executor(
            'balanced', self._balanced_scoring
        )
        self.strategy_router.register_strategy_executor(
            'conservative', self._conservative_scoring
        )
        self.strategy_router.register_strategy_executor(
            'minimal', self._minimal_scoring
        )
    
    def _high_precision_scoring(self, request: ScoringRequest, config: ScoringConfig) -> Dict[str, Any]:
        """高精度打分实现"""
        # 实现高精度打分逻辑
        # 多批次并行打分
        return self._execute_parallel_scoring(request, config, "high_precision")
    
    def _balanced_scoring(self, request: ScoringRequest, config: ScoringConfig) -> Dict[str, Any]:
        """平衡打分实现"""
        return self._execute_parallel_scoring(request, config, "balanced")
    
    def _conservative_scoring(self, request: ScoringRequest, config: ScoringConfig) -> Dict[str, Any]:
        """保守打分实现"""
        return self._execute_parallel_scoring(request, config, "conservative")
    
    def _minimal_scoring(self, request: ScoringRequest, config: ScoringConfig) -> Dict[str, Any]:
        """最小打分实现"""
        return self._execute_parallel_scoring(request, config, "minimal")
    
    def _execute_parallel_scoring(self, request: ScoringRequest, 
                                config: ScoringConfig, strategy_name: str) -> Dict[str, Any]:
        """执行并行打分"""
        # 这里应该实现具体的并行打分逻辑
        # 根据 batch_count 和 parallelism 进行分批并行处理
        
        logger.info(f"执行 {strategy_name} 策略打分: "
                   f"批次={config.batch_count}, 并行度={config.parallelism}, "
                   f"打分数量={config.scoring_item_count}")
        
        # 模拟打分结果
        return {
            'strategy': strategy_name,
            'user_id': request.user_id,
            'config': config.__dict__,
            'scored_items': config.scoring_item_count,
            'execution_time_ms': config.max_rt * 0.8,  # 模拟执行时间
            'scores': [
                {'item_id': f'item_{i}', 'score': 0.9 - i * 0.001}
                for i in range(config.scoring_item_count)
            ]
        }
    
    def get_recommendations(self, user_id: str, candidate_items: list, 
                          context: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取推荐结果"""
        request = ScoringRequest(
            user_id=user_id,
            items=candidate_items,
            context=context or {}
        )
        
        return self.strategy_router.execute_scoring(request)
