"""
配置管理器测试
"""
import pytest
import json
import tempfile
import os
from pathlib import Path
import time

from src.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        self.test_config = {
            "version": "1.0.0",
            "systemConfig": {
                "bizName": "test-service",
                "maxCapacityQPS": 5000
            },
            "strategies": {
                "test_strategy": {
                    "batchCount": 2,
                    "parallelism": 2
                }
            },
            "routingConfig": {
                "currentStrategy": "test_strategy",
                "trafficDistribution": {
                    "test_strategy": 100.0
                }
            }
        }
        
        json.dump(self.test_config, self.temp_file, indent=2)
        self.temp_file.close()
        
        self.config_manager = ConfigManager(self.temp_file.name)
    
    def teardown_method(self):
        """测试后清理"""
        if hasattr(self, 'config_manager'):
            self.config_manager.stop_monitoring()
        
        if hasattr(self, 'temp_file'):
            try:
                os.unlink(self.temp_file.name)
            except FileNotFoundError:
                pass
    
    def test_load_config(self):
        """测试配置加载"""
        assert self.config_manager.load_config() is True
        
        # 验证配置内容
        config = self.config_manager.get_config()
        assert config['version'] == '1.0.0'
        assert config['systemConfig']['bizName'] == 'test-service'
    
    def test_get_config(self):
        """测试获取配置"""
        # 获取完整配置
        full_config = self.config_manager.get_config()
        assert isinstance(full_config, dict)
        assert 'version' in full_config
        
        # 获取特定路径的配置
        biz_name = self.config_manager.get_config('systemConfig.bizName')
        assert biz_name == 'test-service'
        
        # 获取不存在的配置
        non_existent = self.config_manager.get_config('non.existent.path')
        assert non_existent is None
    
    def test_update_config(self):
        """测试配置更新"""
        # 更新配置
        success = self.config_manager.update_config('systemConfig.bizName', 'updated-service')
        assert success is True
        
        # 验证更新结果
        updated_name = self.config_manager.get_config('systemConfig.bizName')
        assert updated_name == 'updated-service'
        
        # 验证文件已更新
        with open(self.temp_file.name, 'r') as f:
            file_config = json.load(f)
        assert file_config['systemConfig']['bizName'] == 'updated-service'
    
    def test_save_config(self):
        """测试配置保存"""
        new_config = self.test_config.copy()
        new_config['systemConfig']['maxCapacityQPS'] = 8000
        
        success = self.config_manager.save_config(new_config)
        assert success is True
        
        # 验证保存结果
        max_qps = self.config_manager.get_config('systemConfig.maxCapacityQPS')
        assert max_qps == 8000
    
    def test_get_current_strategy(self):
        """测试获取当前策略"""
        strategy = self.config_manager.get_current_strategy()
        assert strategy == 'test_strategy'
    
    def test_get_traffic_distribution(self):
        """测试获取流量分配"""
        distribution = self.config_manager.get_traffic_distribution()
        assert isinstance(distribution, dict)
        assert distribution['test_strategy'] == 100.0
    
    def test_update_traffic_distribution(self):
        """测试更新流量分配"""
        new_distribution = {
            'strategy_a': 60.0,
            'strategy_b': 40.0
        }
        
        success = self.config_manager.update_traffic_distribution(new_distribution)
        assert success is True
        
        # 验证更新结果
        updated_distribution = self.config_manager.get_traffic_distribution()
        assert updated_distribution == new_distribution
    
    def test_get_strategies(self):
        """测试获取策略配置"""
        strategies = self.config_manager.get_strategies()
        assert isinstance(strategies, dict)
        assert 'test_strategy' in strategies
        assert strategies['test_strategy']['batchCount'] == 2
    
    def test_config_listener(self):
        """测试配置变更监听器"""
        callback_called = False
        received_config = None
        
        def test_callback(config):
            nonlocal callback_called, received_config
            callback_called = True
            received_config = config
        
        # 添加监听器
        self.config_manager.add_listener(test_callback)
        
        # 更新配置
        self.config_manager.update_config('systemConfig.bizName', 'listener-test')
        
        # 验证监听器被调用
        assert callback_called is True
        assert received_config is not None
        assert received_config['systemConfig']['bizName'] == 'listener-test'
        
        # 移除监听器
        self.config_manager.remove_listener(test_callback)
    
    def test_file_monitoring(self):
        """测试文件监控"""
        # 启动监控
        self.config_manager.start_monitoring()
        
        # 等待监控启动
        time.sleep(0.1)
        
        # 修改文件
        modified_config = self.test_config.copy()
        modified_config['systemConfig']['bizName'] = 'monitored-service'
        
        with open(self.temp_file.name, 'w') as f:
            json.dump(modified_config, f, indent=2)
        
        # 等待文件监控检测到变化
        time.sleep(1)
        
        # 验证配置已更新
        biz_name = self.config_manager.get_config('systemConfig.bizName')
        assert biz_name == 'monitored-service'
        
        # 停止监控
        self.config_manager.stop_monitoring()
    
    def test_invalid_config_file(self):
        """测试无效配置文件"""
        # 创建无效的JSON文件
        invalid_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        invalid_file.write('invalid json content')
        invalid_file.close()
        
        try:
            invalid_config_manager = ConfigManager(invalid_file.name)
            success = invalid_config_manager.load_config()
            assert success is False
        finally:
            os.unlink(invalid_file.name)
    
    def test_nonexistent_config_file(self):
        """测试不存在的配置文件"""
        nonexistent_path = '/tmp/nonexistent_config.json'
        config_manager = ConfigManager(nonexistent_path)
        success = config_manager.load_config()
        assert success is False


if __name__ == '__main__':
    pytest.main([__file__])
